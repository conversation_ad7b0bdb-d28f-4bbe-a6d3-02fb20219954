{"id": "cf460274-5cfc-4396-8f61-4978ca74d9f1", "version": "1.0.0", "created_at": "2025-08-08T08:18:41.832352Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:18:41.832353Z", "device_id": "QtYL87oZ4UFswle32UE0eQ", "hardware_signature": {"cpu_signature": "apple-apple-silicon-131", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "bWFLHs9Lnxx1tjEPT1LKZw", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "PtFkNdJWrL1QM9VSbeNhLA", "username_hash": "Y2AAc0664lrLJVOtLD5Dow"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "EkLEpKsXpgBiGfuSwvkPoQ", "session_signature": "edPJFgapDRY", "workspace_signature": "LbHrouaBXlA", "extensions_signature": "wRI86agHHXs"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-C3LrRw6k", "network_class": "ethernet", "connectivity_signature": "SBmu5ZlH0NU"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "d317cd972b629d7a680829a7be9d13ba6f62807b1fc714e1640bdcb0f3721b85"}
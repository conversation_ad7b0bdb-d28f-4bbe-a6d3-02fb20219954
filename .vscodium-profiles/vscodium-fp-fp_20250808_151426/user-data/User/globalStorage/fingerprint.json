{"id": "b5d89420-8631-4a49-92a5-faad50d25f55", "version": "1.0.0", "created_at": "2025-08-08T08:14:26.665227Z", "privacy_level": "High", "rotation_enabled": true, "next_rotation": "2025-08-09T08:14:26.665227Z", "device_id": "fWWpglagoFTcc9mVkLiLcQ", "hardware_signature": {"cpu_signature": "apple-apple-silicon-457", "memory_class": "4-8GB", "architecture": "aarch64", "hardware_uuid_hash": "Jcb_gxxz2hBuKQOA-kiNnQ", "performance_class": "high"}, "system_signature": {"os_family": "macos", "os_version_class": "macos-recent", "locale_region": "US", "timezone_offset": 7, "hostname_hash": "CpV5N-u8myhjF85P3IwYCg", "username_hash": "7TauFGULWTtFHNl2h0Iwwg"}, "vscode_signature": {"version_class": "1.80-1.90", "machine_id_hash": "MIDRh5DrHugakB2YOXRgAA", "session_signature": "WpXjVYxFFrs", "workspace_signature": "KYvskrkePR4", "extensions_signature": "p8zJIDgX4UQ"}, "network_signature": {"interface_count": 1, "mac_signature": "generic-gJKBpMVu", "network_class": "ethernet", "connectivity_signature": "2nfWjSbmX9I"}, "privacy_metadata": {"anonymization_applied": ["hostname_hashing", "username_hashing", "cpu_obfuscation", "memory_classification"], "obfuscation_methods": ["sha256_hashing", "range_classification", "differential_privacy"], "data_retention_policy": "24h-rotation", "privacy_score": 0.8, "tracking_resistance": 0.7200000000000001}, "fingerprint_hash": "e7960ee8cedc609d83418dbb897c34a8eb1f8d12bd5adb6f6219ee843e969ca7"}
#!/bin/bash
set -euo pipefail

# Set secure umask
umask 077

# Clear potentially dangerous environment variables
unset SSH_AUTH_SOCK SSH_AGENT_PID GPG_AGENT_INFO

# Set resource limits
ulimit -c 0          # No core dumps
ulimit -f 1048576    # Max file size 1GB
ulimit -n 1024       # Max open files
ulimit -u 512        # Max processes
# Virtual memory limit (macOS compatible)
if [[ "$(uname)" == "Darwin" ]]; then
    ulimit -d 2097152 2>/dev/null || true  # Max data segment 2GB (macOS)
else
    ulimit -v 2097152 2>/dev/null || true  # Max virtual memory 2GB (Linux)
fi

# Ensure secure permissions on runtime directories
chmod 700 "$TMPDIR" "$XDG_RUNTIME_DIR" 2>/dev/null || true

# Launch VSCodium with security options
exec codium \
    --no-sandbox \
    --disable-dev-shm-usage \
    --disable-gpu-sandbox \
    --disable-software-rasterizer \
    --disable-background-timer-throttling \
    --disable-backgrounding-occluded-windows \
    --disable-renderer-backgrounding \
    --disable-features=TranslateUI,VizDisplayCompositor \
    "$@"

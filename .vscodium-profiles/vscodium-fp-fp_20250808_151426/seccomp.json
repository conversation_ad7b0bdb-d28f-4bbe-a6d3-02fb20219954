{"defaultAction": "SCMP_ACT_ERRNO", "architectures": ["SCMP_ARCH_X86_64", "SCMP_ARCH_X86", "SCMP_ARCH_X32"], "syscalls": [{"names": ["accept", "accept4", "access", "adjtimex", "alarm", "bind", "brk", "capget", "capset", "chdir", "chmod", "chown", "chroot", "clock_getres", "clock_gettime", "clock_nanosleep", "close", "connect", "copy_file_range", "creat", "dup", "dup2", "dup3", "epoll_create", "epoll_create1", "epoll_ctl", "epoll_wait", "eventfd", "eventfd2", "execve", "exit", "exit_group", "faccessat", "fadvise64", "fallocate", "fanotify_mark", "fchdir", "fchmod", "fchmodat", "fchown", "fchownat", "fcntl", "fdatasync", "fgetxattr", "fl<PERSON><PERSON><PERSON><PERSON>", "flock", "fork", "fremovexattr", "fsetxattr", "fstat", "fstatfs", "fsync", "ftrun<PERSON>", "futex", "getcwd", "getdents", "getdents64", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getgid", "getgroups", "getitimer", "getpeername", "getpgid", "getpgrp", "getpid", "<PERSON><PERSON><PERSON>", "getpriority", "getrandom", "get<PERSON><PERSON>d", "getresuid", "getrlimit", "getrusage", "getsid", "getsockname", "getsockopt", "gettid", "gettimeofday", "getuid", "getxa<PERSON><PERSON>", "inotify_add_watch", "inotify_init", "inotify_init1", "inotify_rm_watch", "ioctl", "kill", "lchown", "link", "linkat", "listen", "listxattr", "llistxattr", "lremovexattr", "lseek", "lsetxattr", "lstat", "madvise", "memfd_create", "mkdir", "mkdirat", "mknod", "mknodat", "mlock", "m<PERSON>all", "mmap", "mount", "mprotect", "mq_getsetattr", "mq_notify", "mq_open", "mq_timedreceive", "mq_timedsend", "mq_unlink", "mremap", "msync", "munlock", "mun<PERSON><PERSON>", "mun<PERSON>p", "nanosleep", "newfstatat", "open", "openat", "pause", "pipe", "pipe2", "poll", "ppoll", "prctl", "pread64", "preadv", "prlimit64", "pselect6", "ptrace", "pwrite64", "pwritev", "read", "readahead", "readlink", "readlinkat", "readv", "recv", "recvfrom", "recvmsg", "removexattr", "rename", "renameat", "renameat2", "restart_syscall", "rmdir", "rt_sigaction", "rt_sigpending", "rt_sigprocmask", "rt_sigqueueinfo", "rt_sigreturn", "rt_sigsuspend", "rt_sigtimedwait", "rt_tgsigqueueinfo", "sched_getaffinity", "sched_getattr", "sched_getparam", "sched_get_priority_max", "sched_get_priority_min", "sched_getscheduler", "sched_setaffinity", "sched_setattr", "sched_setparam", "sched_setscheduler", "sched_yield", "seccomp", "select", "semctl", "semget", "semop", "send", "sendfile", "sendmsg", "sendto", "setfsgid", "set<PERSON>uid", "<PERSON><PERSON>d", "setgroups", "setitimer", "setpgid", "setpriority", "set<PERSON>gi<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setresuid", "set<PERSON><PERSON>", "setrlimit", "setsid", "setsockopt", "setuid", "<PERSON><PERSON><PERSON><PERSON>", "shmat", "shmctl", "shmdt", "shmget", "shutdown", "sigaltsta<PERSON>", "signalfd", "signalfd4", "socket", "socketpair", "splice", "stat", "statfs", "symlink", "symlinkat", "sync", "sync_file_range", "syncfs", "sysinfo", "tee", "tgkill", "time", "timer_create", "timer_delete", "timer_getoverrun", "timer_gettime", "timer_settime", "times", "tkill", "truncate", "umask", "uname", "unlink", "unlinkat", "utime", "utimensat", "utimes", "vfork", "vmsplice", "wait4", "waitid", "write", "writev"], "action": "SCMP_ACT_ALLOW"}]}
FROM ubuntu:22.04

# Set security-focused build arguments
ARG DEBIAN_FRONTEND=noninteractive
ARG USER_UID=1000
ARG USER_GID=1000

# Install minimal dependencies with security updates
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    wget \
    gpg \
    software-properties-common \
    libasound2 \
    libgtk-3-0 \
    libxss1 \
    libnss3 \
    libgconf-2-4 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgdk-pixbuf2.0-0 \
    libgbm1 \
    libxkbcommon0 \
    libxkbfile1 \
    && apt-get upgrade -y \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Install VSCodium with signature verification
RUN wget -qO - https://gitlab.com/paulcarroty/vscodium-deb-rpm-repo/raw/master/pub.gpg \
    | gpg --dearmor \
    | dd of=/usr/share/keyrings/vscodium-archive-keyring.gpg \
    && echo 'deb [ signed-by=/usr/share/keyrings/vscodium-archive-keyring.gpg ] https://download.vscodium.com/debs vscodium main' \
    | tee /etc/apt/sources.list.d/vscodium.list \
    && apt-get update \
    && apt-get install -y --no-install-recommends codium \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Create non-root user with specific UID/GID
RUN groupadd -g $USER_GID vscodium \
    && useradd -u $USER_UID -g $USER_GID -m -s /bin/bash vscodium \
    && mkdir -p /home/<USER>/.config/VSCodium \
    && chown -R vscodium:vscodium /home/<USER>

# Remove unnecessary packages and clean up
RUN apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/* \
    && rm -rf /root/.cache

# Set up secure directories with proper permissions
RUN mkdir -p /workspace /tmp/vscodium \
    && chown vscodium:vscodium /workspace /tmp/vscodium \
    && chmod 755 /workspace \
    && chmod 700 /tmp/vscodium

# Switch to non-root user
USER vscodium
WORKDIR /workspace

# Set secure environment variables
ENV DISPLAY=:0
ENV HOME=/home/<USER>
ENV TMPDIR=/tmp/vscodium
ENV XDG_RUNTIME_DIR=/tmp/vscodium
ENV XDG_CACHE_HOME=/tmp/vscodium/.cache
ENV XDG_CONFIG_HOME=/home/<USER>/.config
ENV XDG_DATA_HOME=/home/<USER>/.local/share

# Create secure entrypoint script
COPY --chown=vscodium:vscodium entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
